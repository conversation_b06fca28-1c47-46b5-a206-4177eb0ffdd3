import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";

const thgPrice = 0.05;

const getGrossMarginCurrentYear = async (ouCodes: string[]) => {
  const date = new Date();
  const yearStart = new Date(date.getFullYear(), 0, 0);

  const endDate = new Date();
  yearStart.setUTCHours(0, 0, 0, 0);
  endDate.setUTCHours(23, 59, 59, 999);

  const cdrs = await prisma.cdr.findMany({
    where: {
      End_datetime: {
        gte: yearStart,
        lte: endDate,
      },
      OU_Code: { in: ouCodes.map(code => `'${code}'`) },
    },
    include: {
      cost: true,
      tarif: true,
    },
  });

  const kWh = cdrs.reduce((acc, cdr) => acc + (cdr.Volume || 0), 0);
  const revenue = cdrs.reduce((acc, cdr) => acc + (cdr.Calculated_Cost || 0), 0);
  const energyCost = cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);
  const energyGrossMargin = revenue - energyCost;
  const grossMargin = energyGrossMargin + thgPrice * kWh;

  const diff = date.getTime() - yearStart.getTime();
  const oneHour = 1000 * 60 * 60;
  const hourOfYear = Math.floor(diff / oneHour);

  const isLeapYear =
    (date.getFullYear() % 4 == 0 && date.getFullYear() % 100 != 0) || date.getFullYear() % 400 == 0;
  const totalHoursOfYear = isLeapYear ? 366 * 24 : 365 * 24;

  return {
    thgUntilNow: thgPrice * kWh,
    energyGrossMarginUntilNow: energyGrossMargin,
    kWhUntilNow: kWh,
    thgCurrentYear: ((thgPrice * kWh) / hourOfYear) * totalHoursOfYear,
    energyGrossMarginCurrentYear: (energyGrossMargin / hourOfYear) * totalHoursOfYear,
    grossMarginCurrentYear: (grossMargin / hourOfYear) * totalHoursOfYear,
    kWhCurrentYear: (kWh / hourOfYear) * totalHoursOfYear,
  };
};

const getGrossMarginCurrentMonth = async (ouCodes: string[]) => {
  const date = new Date();
  const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);

  const endDate = new Date();
  monthStart.setUTCHours(0, 0, 0, 0);
  endDate.setUTCHours(23, 59, 59, 999);

  const cdrs = await prisma.cdr.findMany({
    where: {
      End_datetime: {
        gte: monthStart,
        lte: endDate,
      },
      OU_Code: { in: ouCodes.map(code => `'${code}'`) },
    },
    include: {
      cost: true,
      tarif: true,
    },
  });

  const kWh = cdrs.reduce((acc, cdr) => acc + (cdr.Volume || 0), 0);
  const revenue = cdrs.reduce((acc, cdr) => acc + (cdr.Calculated_Cost || 0), 0);
  const energyCost = cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);
  const energyGrossMargin = revenue - energyCost;
  const grossMargin = energyGrossMargin + thgPrice * kWh;

  const diff = date.getTime() - monthStart.getTime();
  const oneHour = 1000 * 60 * 60;
  const hourOfMonth = Math.floor(diff / oneHour);

  // Get total hours in current month
  const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  const totalHoursOfMonth = daysInMonth * 24;

  return {
    thgUntilNowMonth: thgPrice * kWh,
    energyGrossMarginUntilNowMonth: energyGrossMargin,
    kWhUntilNowMonth: kWh,
    thgCurrentMonth: hourOfMonth > 0 ? ((thgPrice * kWh) / hourOfMonth) * totalHoursOfMonth : 0,
    energyGrossMarginCurrentMonth: hourOfMonth > 0 ? (energyGrossMargin / hourOfMonth) * totalHoursOfMonth : 0,
    grossMarginCurrentMonth: hourOfMonth > 0 ? (grossMargin / hourOfMonth) * totalHoursOfMonth : 0,
    kWhCurrentMonth: hourOfMonth > 0 ? (kWh / hourOfMonth) * totalHoursOfMonth : 0,
  };
};

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get OUs that the user has access to
    const ouWithChildren = await getOusBelowOu(session.user.selectedOu);
    const ouCodes = ouWithChildren?.map((ou) => ou.code) || [];

    // Get both year and month data
    const yearData = await getGrossMarginCurrentYear(ouCodes);
    const monthData = await getGrossMarginCurrentMonth(ouCodes);

    // Combine the data
    const combinedData = {
      ...yearData,
      ...monthData,
    };

    return NextResponse.json({
      status: "success",
      data: combinedData,
    });
  } catch (error) {
    console.error("Error fetching current forecast data:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
