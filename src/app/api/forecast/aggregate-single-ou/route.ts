import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const thgPrice = 0.05;

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check for internal API key for cronjob access
    const apiKey = request.headers.get('x-api-key');
    const isInternalCall = apiKey === (process.env.INTERNAL_API_KEY || 'internal-cronjob-key');
    
    if (!isInternalCall && (!session || session?.user?.role !== Role.ADMIN)) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    const body = await request.json();
    const { ouCode, months } = body;

    if (!ouCode) {
      return NextResponse.json({ error: "ouCode is required" }, { status: 400 });
    }

    Logger(`Starting single OU aggregation for OU: ${ouCode}`, "Single OU Forecast Aggregation", "api", LogType.INFO);

    // Get the current month key for comparison
    const currentDate = new Date();
    const currentMonthKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

    // If no specific months provided, aggregate last 24 months
    let monthsToProcess: string[] = [];
    
    if (months && Array.isArray(months)) {
      monthsToProcess = months;
    } else {
      // Generate last 24 months
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 24);
      
      const tempDate = new Date(startDate);
      while (tempDate <= endDate) {
        const monthKey = `${tempDate.getFullYear()}-${String(tempDate.getMonth() + 1).padStart(2, '0')}`;
        monthsToProcess.push(monthKey);
        tempDate.setMonth(tempDate.getMonth() + 1);
      }
    }

    let totalProcessed = 0;
    let totalSkipped = 0;
    let totalUpdated = 0;

    for (const monthKey of monthsToProcess) {
      // Check if aggregation already exists for this month and OU
      const existingAggregation = await prisma.monthlyForecastAggregation.findUnique({
        where: {
          month_ouCode: {
            month: monthKey,
            ouCode: ouCode,
          },
        },
      });

      // Skip if data exists and it's not the current month
      if (existingAggregation && monthKey !== currentMonthKey) {
        Logger(
          `Skipping aggregation for ${monthKey} - ${ouCode}: Data already exists`,
          "Single OU Forecast Aggregation",
          "api",
          LogType.DEBUG
        );
        totalSkipped++;
        continue;
      }

      // Parse month key to get date boundaries
      const [year, month] = monthKey.split('-').map(Number);
      const monthStart = new Date(year, month - 1, 1);
      const monthEnd = new Date(year, month, 0, 23, 59, 59, 999);
      
      // Get CDRs for this month and OU
      const cdrs = await prisma.cdr.findMany({
        where: {
          End_datetime: {
            gte: monthStart,
            lte: monthEnd,
          },
          OU_Code: `'${ouCode}'`, // Note: OU_Code is stored with quotes
        },
        include: {
          cost: true,
          tarif: true,
        },
      });

      // Calculate aggregated values
      const kWh = cdrs.reduce((acc, cdr) => acc + (cdr.Volume || 0), 0);
      const revenue = cdrs.reduce((acc, cdr) => acc + (cdr.Calculated_Cost || 0), 0);
      const energyCost = cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);
      const energyGrossMargin = revenue - energyCost;
      const thgRevenue = thgPrice * kWh;
      const totalGrossMargin = energyGrossMargin + thgRevenue;
      const sessions = cdrs.length;

      if (existingAggregation) {
        // Update existing record (current month only)
        await prisma.monthlyForecastAggregation.update({
          where: {
            id: existingAggregation.id,
          },
          data: {
            revenue,
            energyCost,
            energyGrossMargin,
            thgRevenue,
            totalGrossMargin,
            kWh,
            sessions,
            updatedAt: new Date(),
          },
        });
        Logger(
          `Updated aggregation for ${monthKey} - ${ouCode}`,
          "Single OU Forecast Aggregation",
          "api",
          LogType.DEBUG
        );
      } else {
        // Create new record
        await prisma.monthlyForecastAggregation.create({
          data: {
            month: monthKey,
            ouCode: ouCode,
            revenue,
            energyCost,
            energyGrossMargin,
            thgRevenue,
            totalGrossMargin,
            kWh,
            sessions,
          },
        });
        Logger(
          `Created aggregation for ${monthKey} - ${ouCode}`,
          "Single OU Forecast Aggregation",
          "api",
          LogType.DEBUG
        );
      }

      totalProcessed++;
      if (kWh > 0 || revenue > 0) {
        totalUpdated++;
      }
    }

    Logger(
      `Single OU aggregation completed for ${ouCode}. Processed: ${totalProcessed}, Skipped: ${totalSkipped}, Updated: ${totalUpdated}`,
      "Single OU Forecast Aggregation",
      "api",
      LogType.INFO
    );

    return NextResponse.json({
      status: "success",
      message: `Single OU aggregation completed successfully for ${ouCode}`,
      data: {
        ouCode,
        totalProcessed,
        totalSkipped,
        totalUpdated,
        monthsRequested: monthsToProcess.length,
      },
    });
  } catch (error) {
    Logger(
      `Error in single OU forecast aggregation: ${error}`,
      "Single OU Forecast Aggregation",
      "api",
      LogType.ERROR
    );
    console.error("Error in single OU forecast aggregation:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Internal server error during single OU aggregation",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
