import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const thgPrice = 0.05;

export async function POST() {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session?.user?.role !== Role.ADMIN) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    Logger("Starting monthly forecast aggregation", "Monthly Forecast Aggregation", "api", LogType.INFO);

    // Get all OUs
    const ous = await prisma.ou.findMany({
      select: {
        id: true,
        code: true,
      },
    });

    // Get the date range for the last 24 months
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 24);
    startDate.setDate(1); // Start from the first day of the month
    startDate.setHours(0, 0, 0, 0);

    let totalProcessed = 0;
    let totalUpdated = 0;

    for (const ou of ous) {
      // Generate months for this OU
      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const monthKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
        
        // Calculate month boundaries
        const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59, 999);
        
        // Get CDRs for this month and OU
        const cdrs = await prisma.cdr.findMany({
          where: {
            End_datetime: {
              gte: monthStart,
              lte: monthEnd,
            },
            OU_Code: `'${ou.code}'`, // Note: OU_Code is stored with quotes
          },
          include: {
            cost: true,
            tarif: true,
          },
        });

        // Calculate aggregated values
        const kWh = cdrs.reduce((acc, cdr) => acc + (cdr.Volume || 0), 0);
        const revenue = cdrs.reduce((acc, cdr) => acc + (cdr.Calculated_Cost || 0), 0);
        const energyCost = cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);
        const energyGrossMargin = revenue - energyCost;
        const thgRevenue = thgPrice * kWh;
        const totalGrossMargin = energyGrossMargin + thgRevenue;
        const sessions = cdrs.length;

        // Upsert the aggregation record
        await prisma.monthlyForecastAggregation.upsert({
          where: {
            month_ouCode: {
              month: monthKey,
              ouCode: ou.code,
            },
          },
          update: {
            revenue,
            energyCost,
            energyGrossMargin,
            thgRevenue,
            totalGrossMargin,
            kWh,
            sessions,
            updatedAt: new Date(),
          },
          create: {
            month: monthKey,
            ouCode: ou.code,
            revenue,
            energyCost,
            energyGrossMargin,
            thgRevenue,
            totalGrossMargin,
            kWh,
            sessions,
          },
        });

        totalProcessed++;
        if (kWh > 0 || revenue > 0) {
          totalUpdated++;
        }

        // Move to next month
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }

    Logger(
      `Monthly forecast aggregation completed. Processed: ${totalProcessed}, Updated: ${totalUpdated}`,
      "Monthly Forecast Aggregation",
      "api",
      LogType.INFO
    );

    return NextResponse.json({
      status: "success",
      message: `Monthly forecast aggregation completed successfully`,
      data: {
        totalProcessed,
        totalUpdated,
        ousProcessed: ous.length,
      },
    });
  } catch (error) {
    Logger(
      `Error in monthly forecast aggregation: ${error}`,
      "Monthly Forecast Aggregation",
      "api",
      LogType.ERROR
    );
    console.error("Error in monthly forecast aggregation:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Internal server error during aggregation",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
