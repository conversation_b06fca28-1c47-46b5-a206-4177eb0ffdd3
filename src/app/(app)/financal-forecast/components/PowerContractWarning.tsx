"use client";

import React, { useState, useEffect } from "react";
import { FaExclamationTriangle, FaInfoCircle, FaTimes, FaChevronDown, FaChevronUp } from "react-icons/fa";
import { userStore } from "~/server/zustand/store";

interface PowerContractWarningData {
  warningLevel: 'none' | 'low' | 'medium' | 'high';
  summary: {
    totalLocations: number;
    locationsWithoutContracts: number;
    percentageWithoutContracts: number;
    recentCdrsCount: number;
    recentCdrsVolume: number;
  };
  locationsWithoutContracts: Array<{
    id: string;
    name: string;
    city: string;
    ouName: string;
    ouCode: string;
    evseCount: number;
    activeEvses: number;
  }>;
}

const PowerContractWarning = () => {
  const [warningData, setWarningData] = useState<PowerContractWarningData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dismissed, setDismissed] = useState(false);
  const [expanded, setExpanded] = useState(false);
  
  // Subscribe to OU changes
  const { selectedOuId } = userStore();

  useEffect(() => {
    const fetchWarningData = async () => {
      try {
        setLoading(true);
        
        const url = selectedOuId 
          ? `/api/forecast/power-contract-warnings?ouId=${encodeURIComponent(selectedOuId)}`
          : "/api/forecast/power-contract-warnings";
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.status === "success") {
          setWarningData(result.data);
        }
      } catch (err) {
        console.error("Error fetching power contract warnings:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchWarningData();
  }, [selectedOuId]);

  // Don't show anything if loading, dismissed, or no warning
  if (loading || dismissed || !warningData || warningData.warningLevel === 'none') {
    return null;
  }

  const getWarningConfig = () => {
    switch (warningData.warningLevel) {
      case 'high':
        return {
          bgColor: 'bg-blue-50 border-blue-200',
          textColor: 'text-blue-700',
          iconColor: 'text-blue-500',
          icon: FaInfoCircle,
          title: 'Hinweis zu Stromverträgen',
        };
      case 'medium':
        return {
          bgColor: 'bg-blue-50 border-blue-200',
          textColor: 'text-blue-700',
          iconColor: 'text-blue-500',
          icon: FaInfoCircle,
          title: 'Hinweis zu Stromverträgen',
        };
      case 'low':
        return {
          bgColor: 'bg-gray-50 border-gray-200',
          textColor: 'text-gray-700',
          iconColor: 'text-gray-500',
          icon: FaInfoCircle,
          title: 'Hinweis zu Stromverträgen',
        };
      default:
        return {
          bgColor: 'bg-gray-50 border-gray-200',
          textColor: 'text-gray-700',
          iconColor: 'text-gray-500',
          icon: FaInfoCircle,
          title: 'Information: Stromverträge',
        };
    }
  };

  const config = getWarningConfig();
  const Icon = config.icon;

  return (
    <div className={`rounded-lg border p-4 mt-4 ${config.bgColor}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <Icon className={`mt-0.5 ${config.iconColor}`} size={16} />
          <div className="flex-1">
            <h3 className={`text-sm font-medium ${config.textColor}`}>
              {config.title}
            </h3>
            <div className={`mt-1 text-sm ${config.textColor}`}>
              <p className="text-sm">
                {warningData.summary.locationsWithoutContracts} von{" "}
                {warningData.summary.totalLocations} Standorten{" "}
                ({warningData.summary.percentageWithoutContracts}%) haben keinen gültigen Stromvertrag.
              </p>
              {warningData.summary.recentCdrsCount > 0 && (
                <p className="mt-1 text-sm opacity-75">
                  In den letzten 30 Tagen: {warningData.summary.recentCdrsCount} Ladevorgänge{" "}
                  mit {warningData.summary.recentCdrsVolume} kWh an diesen Standorten.
                </p>
              )}
              <p className="mt-2 text-sm">
                💡 Für präzisere Margenberechnungen empfehlen wir, Stromverträge für alle Standorte zu hinterlegen.
              </p>
            </div>
            
            {warningData.locationsWithoutContracts.length > 0 && (
              <button
                onClick={() => setExpanded(!expanded)}
                className={`mt-2 flex items-center space-x-1 text-xs ${config.textColor} hover:underline opacity-75 hover:opacity-100`}
              >
                {expanded ? <FaChevronUp size={10} /> : <FaChevronDown size={10} />}
                <span>
                  {expanded ? 'Weniger anzeigen' : `${warningData.locationsWithoutContracts.length} betroffene Standorte anzeigen`}
                </span>
              </button>
            )}
            
            {expanded && (
              <div className="mt-3 space-y-2">
                {warningData.locationsWithoutContracts.map((location) => (
                  <div
                    key={location.id}
                    className={`rounded border p-2 text-xs ${config.bgColor.replace('50', '100')}`}
                  >
                    <div className="font-medium">{location.name}</div>
                    <div className={config.textColor}>
                      {location.city} • {location.ouName} ({location.ouCode}) • {location.evseCount} Ladepunkte
                      {location.activeEvses > 0 && ` • ${location.activeEvses} aktiv`}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <button
          onClick={() => setDismissed(true)}
          className={`ml-2 ${config.textColor} opacity-50 hover:opacity-70`}
          title="Hinweis ausblenden"
        >
          <FaTimes size={14} />
        </button>
      </div>
    </div>
  );
};

export default PowerContractWarning;
