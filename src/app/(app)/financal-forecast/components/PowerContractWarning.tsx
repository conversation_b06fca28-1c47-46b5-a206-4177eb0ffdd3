"use client";

import React, { useState, useEffect } from "react";
import { FaExclamationTriangle, FaInfoCircle, FaTimes, FaChevronDown, FaChevronUp } from "react-icons/fa";
import { userStore } from "~/server/zustand/store";

interface PowerContractWarningData {
  warningLevel: 'none' | 'low' | 'medium' | 'high';
  summary: {
    totalLocations: number;
    locationsWithoutContracts: number;
    percentageWithoutContracts: number;
    recentCdrsCount: number;
    recentCdrsVolume: number;
  };
  locationsWithoutContracts: Array<{
    id: string;
    name: string;
    city: string;
    ouName: string;
    ouCode: string;
    evseCount: number;
    activeEvses: number;
  }>;
}

const PowerContractWarning = () => {
  const [warningData, setWarningData] = useState<PowerContractWarningData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dismissed, setDismissed] = useState(false);
  const [expanded, setExpanded] = useState(false);
  
  // Subscribe to OU changes
  const { selectedOuId } = userStore();

  useEffect(() => {
    const fetchWarningData = async () => {
      try {
        setLoading(true);
        
        const url = selectedOuId 
          ? `/api/forecast/power-contract-warnings?ouId=${encodeURIComponent(selectedOuId)}`
          : "/api/forecast/power-contract-warnings";
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.status === "success") {
          setWarningData(result.data);
        }
      } catch (err) {
        console.error("Error fetching power contract warnings:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchWarningData();
  }, [selectedOuId]);

  // Don't show anything if loading, dismissed, or no warning
  if (loading || dismissed || !warningData || warningData.warningLevel === 'none') {
    return null;
  }

  const getWarningConfig = () => {
    switch (warningData.warningLevel) {
      case 'high':
        return {
          bgColor: 'bg-red-50 border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-600',
          icon: FaExclamationTriangle,
          title: 'Kritische Warnung: Fehlende Stromverträge',
        };
      case 'medium':
        return {
          bgColor: 'bg-orange-50 border-orange-200',
          textColor: 'text-orange-800',
          iconColor: 'text-orange-600',
          icon: FaExclamationTriangle,
          title: 'Warnung: Fehlende Stromverträge',
        };
      case 'low':
        return {
          bgColor: 'bg-yellow-50 border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600',
          icon: FaInfoCircle,
          title: 'Hinweis: Fehlende Stromverträge',
        };
      default:
        return {
          bgColor: 'bg-blue-50 border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600',
          icon: FaInfoCircle,
          title: 'Information: Stromverträge',
        };
    }
  };

  const config = getWarningConfig();
  const Icon = config.icon;

  return (
    <div className={`rounded-lg border p-4 mb-5 ${config.bgColor}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <Icon className={`mt-0.5 ${config.iconColor}`} size={20} />
          <div className="flex-1">
            <h3 className={`font-medium ${config.textColor}`}>
              {config.title}
            </h3>
            <div className={`mt-1 text-sm ${config.textColor}`}>
              <p>
                <strong>{warningData.summary.locationsWithoutContracts}</strong> von{" "}
                <strong>{warningData.summary.totalLocations}</strong> Standorten{" "}
                ({warningData.summary.percentageWithoutContracts}%) haben keinen gültigen Stromvertrag.
              </p>
              {warningData.summary.recentCdrsCount > 0 && (
                <p className="mt-1">
                  In den letzten 30 Tagen gab es <strong>{warningData.summary.recentCdrsCount}</strong> Ladevorgänge{" "}
                  mit <strong>{warningData.summary.recentCdrsVolume} kWh</strong> an diesen Standorten.
                </p>
              )}
              <p className="mt-2 font-medium">
                ⚠️ Die Margenberechnung ist ohne Stromverträge nicht korrekt, da die Energiekosten fehlen.
              </p>
            </div>
            
            {warningData.locationsWithoutContracts.length > 0 && (
              <button
                onClick={() => setExpanded(!expanded)}
                className={`mt-2 flex items-center space-x-1 text-sm font-medium ${config.textColor} hover:underline`}
              >
                {expanded ? <FaChevronUp size={12} /> : <FaChevronDown size={12} />}
                <span>
                  {expanded ? 'Weniger anzeigen' : `${warningData.locationsWithoutContracts.length} betroffene Standorte anzeigen`}
                </span>
              </button>
            )}
            
            {expanded && (
              <div className="mt-3 space-y-2">
                {warningData.locationsWithoutContracts.map((location) => (
                  <div
                    key={location.id}
                    className={`rounded border p-2 text-xs ${config.bgColor.replace('50', '100')}`}
                  >
                    <div className="font-medium">{location.name}</div>
                    <div className={config.textColor}>
                      {location.city} • {location.ouName} ({location.ouCode}) • {location.evseCount} Ladepunkte
                      {location.activeEvses > 0 && ` • ${location.activeEvses} aktiv`}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <button
          onClick={() => setDismissed(true)}
          className={`ml-2 ${config.textColor} hover:opacity-70`}
          title="Warnung ausblenden"
        >
          <FaTimes size={16} />
        </button>
      </div>
    </div>
  );
};

export default PowerContractWarning;
