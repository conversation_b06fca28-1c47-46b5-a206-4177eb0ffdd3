"use client";

import React from "react";
import ReactECharts from "echarts-for-react";

interface MonthlyForecastData {
  month: string;
  revenue: number;
  energyGrossMargin: number;
  thgRevenue: number;
  totalGrossMargin: number;
  kWh: number;
  sessions: number;
}

interface MonthlyForecastChartProps {
  monthlyData: MonthlyForecastData[];
}

const MonthlyForecastChart = ({ monthlyData }: MonthlyForecastChartProps) => {
  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const monthNames = [
      "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
      "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
    }).format(value);
  };

  // Calculate trend lines using linear regression
  const calculateTrendLine = (data: number[]) => {
    const n = data.length;
    if (n < 2) return data.map(() => null);

    const xSum = data.reduce((sum, _, i) => sum + i, 0);
    const ySum = data.reduce((sum, val) => sum + val, 0);
    const xySum = data.reduce((sum, val, i) => sum + i * val, 0);
    const x2Sum = data.reduce((sum, _, i) => sum + i * i, 0);

    const slope = (n * xySum - xSum * ySum) / (n * x2Sum - xSum * xSum);
    const intercept = (ySum - slope * xSum) / n;

    return data.map((_, i) => slope * i + intercept);
  };

  // Prepare data for the chart
  const months = monthlyData.map(d => d.month);
  const revenueData = monthlyData.map(d => d.revenue);
  const marginData = monthlyData.map(d => d.totalGrossMargin);
  
  // Calculate trend lines
  const revenueTrend = calculateTrendLine(revenueData);
  const marginTrend = calculateTrendLine(marginData);

  const options = {
    title: {
      text: "Monatlicher Umsatz und Marge Trend",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "axis",
      formatter: function(params: any) {
        let result = `<strong>${formatMonth(params[0].axisValue)}</strong><br/>`;
        params.forEach((param: any) => {
          if (param.value !== null && !param.seriesName.includes("Trend")) {
            result += `${param.seriesName}: ${formatCurrency(param.value)}<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: ["Umsatz", "Gesamtmarge", "Umsatz Trend", "Marge Trend"],
      top: 35,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "20%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: months.map(formatMonth),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
      },
    },
    yAxis: {
      type: "value",
      name: "Betrag (€)",
      nameLocation: "middle",
      nameGap: 50,
      axisLabel: {
        formatter: function(value: number) {
          return formatCurrency(value);
        },
        fontSize: 10,
      },
    },
    series: [
      {
        name: "Umsatz",
        type: "line",
        data: revenueData,
        lineStyle: {
          color: "#3b82f6",
          width: 3,
        },
        itemStyle: {
          color: "#3b82f6",
        },
        symbol: "circle",
        symbolSize: 6,
        connectNulls: false,
      },
      {
        name: "Gesamtmarge",
        type: "line",
        data: marginData,
        lineStyle: {
          color: "#10b981",
          width: 3,
        },
        itemStyle: {
          color: "#10b981",
        },
        symbol: "circle",
        symbolSize: 6,
        connectNulls: false,
      },
      {
        name: "Umsatz Trend",
        type: "line",
        data: revenueTrend,
        lineStyle: {
          color: "#3b82f6",
          width: 2,
          type: "dashed",
        },
        itemStyle: {
          color: "#3b82f6",
        },
        symbol: "none",
        symbolSize: 0,
        connectNulls: true,
        smooth: true,
      },
      {
        name: "Marge Trend",
        type: "line",
        data: marginTrend,
        lineStyle: {
          color: "#10b981",
          width: 2,
          type: "dashed",
        },
        itemStyle: {
          color: "#10b981",
        },
        symbol: "none",
        symbolSize: 0,
        connectNulls: true,
        smooth: true,
      },
    ],
  };

  return (
    <div className="h-80">
      <ReactECharts option={options} style={{ height: "100%", width: "100%" }} />
    </div>
  );
};

export default MonthlyForecastChart;
