"use client";

import React, { useRef, useState } from "react";
import Card from "~/component/card";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamationCircle } from "react-icons/fa";
import { ApiResponse } from "~/types/api/apiType";
import SingleOuAggregationCommand from "./components/SingleOuAggregationCommand";

interface ICommand {
  name: string;
  description: string;
  endpoint: string;
}

interface ICommandStatus {
  isLoading: boolean;
  response: ApiResponse<any> | null;
  error: string | null;
}

const Commands: ICommand[] = [
  {
    name: "Import Location",
    description: "Import Location from Longship",
    endpoint: "/api/longship/fetchLocation",
  },
  {
    name: "Import Ou",
    description: "Import Ou from Longship",
    endpoint: "/api/longship/fetchOu",
  },
  {
    name: "Import Cdr",
    description: "Import last 10 Days Cdrs from Longship",
    endpoint: "/api/longship/fetchCdr?copy_mode=delta",
  },
  {
    name: "Import Charger",
    description: "Import Charger from Longship",
    endpoint: "/api/longship/fetchCharger",
  },
  {
    name: "Voltego Matcher",
    description:
      "Match Voltego Rechnungen mit Qonto Transaktionen und lädt die Rechnungen zu Qonto hoch.",
    endpoint: "/api/voltego/matcher",
  },
  {
    name: "Calculate CDR Costs (all)",
    description:
      "Calculate CDR Costs for CDRs with missing cost, tarif or Tariff_Name. 5000 per run.",
    endpoint: "/api/cdr/cost/calc",
  },
  {
    name: "Calculate CDR Costs (adhoc)",
    description:
      "Calculate CDR Costs for CDRs with missing cost, tarif or Tariff_Name. Service_Provider_ID = DEEUL, 5000 per run.",
    endpoint: "/api/cdr/cost/calc/adhoc",
  },
  {
    name: "Calculate CDR Costs (roaming)",
    description:
      "Calculate CDR Costs for CDRs with missing cost, tarif or Tariff_Name. 5000 per run.",
    endpoint: "/api/cdr/cost/calc/roaming",
  },
  {
    name: "Aggregate Monthly Forecast",
    description:
      "Aggregate monthly forecast data for the last 24 months for all OUs. Skips existing data except current month. Improves performance of forecast charts.",
    endpoint: "/api/forecast/aggregate-monthly",
  },
];

const CommandTable = () => {
  const [commandStatuses, setCommandStatuses] = useState<Record<string, ICommandStatus>>({});
  const dialog = useRef<HTMLDialogElement>(null);
  const [activeResponse, setActiveResponse] = useState<ApiResponse<any> | null>(null);

  const fetchCommand = async (command: ICommand): Promise<void> => {
    setCommandStatuses((prev) => ({
      ...prev,
      [command.name]: { isLoading: true, error: null, response: null },
    }));

    try {
      const res = await fetch(command.endpoint, {
        method: command.endpoint.includes('aggregate-monthly') ? 'POST' : 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      const response = (await res.json()) as ApiResponse<any>;

      setCommandStatuses((prev) => ({
        ...prev,
        [command.name]: { isLoading: false, response, error: null },
      }));
    } catch (error: any) {
      const errorMessage = "Error fetching data";
      const response: ApiResponse<any> = {
        status: "error",
        message: errorMessage,
        errorCode: error.message,
        errorDetails: error.toString(),
      };
      setCommandStatuses((prev) => ({
        ...prev,
        [command.name]: {
          isLoading: false,
          response,
          error: errorMessage,
        },
      }));
    }
  };

  const handleIconClick = (command: ICommand): void => {
    fetchCommand(command);
  };

  const handleStatusClick = (commandName: string): void => {
    setActiveResponse(commandStatuses[commandName]?.response || null);
    dialog.current?.showModal();
  };

  const handleSingleOuStatusUpdate = (commandName: string, status: any): void => {
    setCommandStatuses((prev) => ({
      ...prev,
      [commandName]: status,
    }));
  };

  const closeModal = (): void => {
    dialog.current?.close();
  };

  return (
    <>
      <table className="min-w-full leading-normal text-primary">
        <thead>
          <tr className="bg-primary text-white">
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Name
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Description
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Command
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Status
            </th>
          </tr>
        </thead>
        <tbody>
          {Commands.map((command) => (
            <tr key={command.name} className="hover:bg-gray-100">
              <td className="border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {command.name}
              </td>
              <td className="border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {command.description}
              </td>
              <td className="border-b border-gray-200 bg-white px-5 py-2 text-sm">
                <button onClick={() => handleIconClick(command)} className="hover:text-blue-800">
                  <FaPlay />
                </button>
              </td>
              <td className="border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {commandStatuses[command.name]?.isLoading ? (
                  <FaSpinner className="animate-spin" />
                ) : commandStatuses[command.name]?.response ? (
                  commandStatuses[command.name]?.response?.status === "success" ? (
                    <button onClick={() => handleStatusClick(command.name)}>
                      <FaCheck className="text-green-500" />
                    </button>
                  ) : (
                    <button onClick={() => handleStatusClick(command.name)}>
                      <FaExclamationCircle className="text-red-500" />
                    </button>
                  )
                ) : null}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {activeResponse && (
        <dialog ref={dialog} className="max-h-screen overflow-y-auto rounded-md p-5">
          <h2 className="text-lg">{activeResponse.status === "success" ? "Success" : "Error"}</h2>
          <p>{activeResponse.message}</p>
          {activeResponse.status === "error" && (
            <div>
              <h3>Error Details:</h3>
              <p>{activeResponse.errorDetails}</p>
            </div>
          )}
          <input type="checkbox" id="togglePayload" className="peer hidden" />
          <label
            htmlFor="togglePayload"
            className="mt-2 inline-block cursor-pointer hover:text-blue-700"
          >
            Show/Hide Response Payload
          </label>
          <div className="mt-4 max-h-0 overflow-y-scroll transition-max-height duration-700 ease-in-out peer-checked:max-h-96">
            <pre className="text-sm text-gray-600">
              {JSON.stringify(activeResponse.payload, null, 2)}
            </pre>
          </div>
          <button
            onClick={closeModal}
            className="mt-4 rounded bg-red-500 px-4 py-2 text-white hover:bg-red-700"
          >
            Close
          </button>
        </dialog>
      )}
    </>
  );
};

const Page: React.FC = () => {
  return (
    <Card>
      <CommandTable />
    </Card>
  );
};

export default Page;
